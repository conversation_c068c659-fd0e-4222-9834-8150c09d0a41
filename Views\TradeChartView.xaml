<UserControl x:Class="BinanceOrderBookWPF.Views.TradeChartView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:BinanceOrderBookWPF.Views"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="400"
             MinWidth="300" MinHeight="200"
             Background="#FF1E1E1E"
             Focusable="True"
             IsTabStop="True">
    
    <UserControl.Resources>
        <!-- Binance官方颜色 -->
        <SolidColorBrush x:Key="BuyBrush" Color="#0ECB81"/>
        <SolidColorBrush x:Key="SellBrush" Color="#F84960"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#1E2329"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#3C4043"/>
        <SolidColorBrush x:Key="TextBrush" Color="#EAECEF"/>
        <SolidColorBrush x:Key="GridLineBrush" Color="#2B3139"/>
        <SolidColorBrush x:Key="AxisBrush" Color="#848E9C"/>
        
        <!-- 图表标题样式 -->
        <Style x:Key="ChartTitleStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#EAECEF"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <!-- 轴标签样式 -->
        <Style x:Key="AxisLabelStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="BinancePlex, Microsoft YaHei"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="#848E9C"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>
    
    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Grid.ColumnSpan="2"
                Background="#2B3139" BorderBrush="#3C4043"
                BorderThickness="0,0,0,1" Height="24">
            <Grid>
                <TextBlock Text="实时交易图表"
                           Foreground="White"
                           FontSize="12"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                    <TextBlock x:Name="TimeWindowDisplay"
                               Text="30分钟"
                               Foreground="#848E9C"
                               FontSize="10"
                               VerticalAlignment="Center"/>
                    <TextBlock Text=" | 滚轮缩放"
                               Foreground="#848E9C"
                               FontSize="9"
                               VerticalAlignment="Center"
                               Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Y轴（价格轴） -->
        <Grid Grid.Row="1" Grid.Column="0" Width="60" Background="#2A2A2A">
            <Canvas x:Name="YAxisCanvas" Background="Transparent"/>
        </Grid>

        <!-- 主图表区域 -->
        <Border Grid.Row="1" Grid.Column="1"
                Background="#1E1E1E"
                BorderBrush="#3C4043"
                BorderThickness="1,0,0,0"
                MouseWheel="ChartArea_MouseWheel"
                Focusable="True">
            <Grid>
                <!-- 网格线层 -->
                <Canvas x:Name="GridCanvas" Background="Transparent"/>

                <!-- 交易点层 -->
                <Canvas x:Name="TradeCanvas" Background="Transparent" ClipToBounds="True"/>

                <!-- 鼠标交互层 -->
                <Canvas x:Name="InteractionCanvas" Background="Transparent"
                        MouseMove="InteractionCanvas_MouseMove"
                        MouseLeave="InteractionCanvas_MouseLeave"/>


            </Grid>
        </Border>

        <!-- X轴（时间轴） -->
        <Grid Grid.Row="2" Grid.Column="1" Height="30" Background="#2A2A2A">
            <Canvas x:Name="XAxisCanvas" Background="Transparent"/>
        </Grid>
        
        <!-- 信息提示框 -->
        <Border x:Name="TooltipBorder" 
                Grid.Row="1" Grid.Column="1"
                Background="#2B3139" 
                BorderBrush="#3C4043" 
                BorderThickness="1"
                CornerRadius="4"
                Padding="8,4"
                Visibility="Collapsed"
                Panel.ZIndex="1000">
            <StackPanel>
                <TextBlock x:Name="TooltipPrice" 
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="11" 
                          Foreground="#EAECEF"/>
                <TextBlock x:Name="TooltipQuantity" 
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="10" 
                          Foreground="#B7BDC6"/>
                <TextBlock x:Name="TooltipTime" 
                          FontFamily="BinancePlex, Microsoft YaHei"
                          FontSize="10" 
                          Foreground="#848E9C"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
