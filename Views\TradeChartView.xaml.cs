using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;
using BinanceOrderBookWPF.Models;
using BinanceOrderBookWPF.Services;

namespace BinanceOrderBookWPF.Views
{
    public partial class TradeChartView : UserControl
    {
        private OrderBookService? _orderBookService;
        private readonly List<TradePoint> _tradePoints = new();
        private readonly Dictionary<Ellipse, TradePoint> _ellipseToTradePoint = new();
        
        // 图表参数
        private double _centerPrice = 0; // 中心价格（最新交易价格）
        private double _priceRange = 100; // Y轴价格范围
        private DateTime _startTime = DateTime.UtcNow;
        private TimeSpan _timeWindow = TimeSpan.FromMinutes(30); // 可变时间窗口，默认30分钟
        private readonly TimeSpan _maxTimeWindow = TimeSpan.FromHours(4); // 最大4小时
        private readonly TimeSpan _minTimeWindow = TimeSpan.FromMinutes(1); // 最小1分钟
        private const double TIME_SCALE = 1.0; // 1秒刻度间隔

        // 防抖机制
        private readonly DispatcherTimer _redrawTimer;
        private bool _needsRedraw = false;

        // 性能优化缓存
        private double _cachedMaxQuantity = 1.0;
        private bool _maxQuantityNeedsUpdate = true;

        // 价格范围计算缓存
        private DateTime _lastPriceRangeUpdate = DateTime.MinValue;
        private readonly TimeSpan _priceRangeUpdateInterval = TimeSpan.FromMilliseconds(500); // 每500ms最多更新一次价格范围
        
        // 颜色定义
        private readonly SolidColorBrush _buyBrush = new(Color.FromRgb(14, 203, 129)) { Opacity = 0.5 };
        private readonly SolidColorBrush _sellBrush = new(Color.FromRgb(248, 73, 96)) { Opacity = 0.5 };
        private readonly SolidColorBrush _gridBrush = new(Color.FromRgb(43, 49, 57));
        private readonly SolidColorBrush _axisBrush = new(Color.FromRgb(132, 142, 156));

        public TradeChartView()
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数开始");
            InitializeComponent();
            Loaded += TradeChartView_Loaded;
            SizeChanged += TradeChartView_SizeChanged;
            DataContextChanged += TradeChartView_DataContextChanged;

            // 添加鼠标滚轮事件处理
            MouseWheel += TradeChartView_MouseWheel;
            MouseEnter += TradeChartView_MouseEnter;

            // 初始化防抖定时器
            _redrawTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 100ms防抖延迟，提供实时显示效果
            };
            _redrawTimer.Tick += RedrawTimer_Tick;

            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数完成");
        }

        private void TradeChartView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 旧值={e.OldValue?.GetType()?.Name}, 新值={e.NewValue?.GetType()?.Name}");

            if (e.NewValue is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 获得OrderBookService，交易数量={orderBookService.RecentTrades?.Count ?? 0}");

                // 如果已经加载完成，立即处理
                if (IsLoaded)
                {
                    System.Diagnostics.Debug.WriteLine("TradeChartView_DataContextChanged: 控件已加载，立即处理DataContext");
                    HandleDataContextChange(orderBookService);
                }
            }
        }

        private void HandleDataContextChange(OrderBookService orderBookService)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");

            // 取消之前的订阅
            if (_orderBookService != null)
            {
                _orderBookService.RecentTrades.CollectionChanged -= RecentTrades_CollectionChanged;
                System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
                Console.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
            }

            _orderBookService = orderBookService;
            _orderBookService.RecentTrades.CollectionChanged += RecentTrades_CollectionChanged;

            System.Diagnostics.Debug.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");
            Console.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");



            // 加载现有的交易数据
            LoadExistingTrades();

            // 绘制图表
            DrawChart();

            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
        }

        private void TradeChartView_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: 开始加载");



            // 初始化时间起点
            _startTime = DateTime.UtcNow;

            // 初始化时间窗口显示
            UpdateTimeWindowDisplay();

            if (DataContext is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: DataContext是OrderBookService，处理数据");
                HandleDataContextChange(orderBookService);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_Loaded: DataContext不是OrderBookService，类型={DataContext?.GetType()?.Name ?? "null"}，添加测试数据");
                // 添加一些测试数据
                AddTestData();
                // 绘制初始图表
                DrawChart();
            }
        }



        private void LoadExistingTrades()
        {
            if (_orderBookService?.RecentTrades != null)
            {
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 开始加载 {_orderBookService.RecentTrades.Count} 个现有交易");
                foreach (var trade in _orderBookService.RecentTrades)
                {
                    AddTradePoint(trade);
                }
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 完成，总交易点数: {_tradePoints.Count}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("LoadExistingTrades: 没有找到交易数据");
            }
        }

        private void AddTestData()
        {
            System.Diagnostics.Debug.WriteLine("AddTestData: 添加测试数据");
            var basePrice = 117600.0;
            var now = DateTime.UtcNow;

            // 添加一些测试交易点
            for (int i = 0; i < 20; i++)
            {
                var tradePoint = new TradePoint
                {
                    Price = basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5),
                    Quantity = 0.1 + (i * 0.05),
                    Time = now.AddSeconds(-60 + i * 3),
                    IsBuyerMaker = i % 2 == 0,
                    Amount = (basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5)) * (0.1 + (i * 0.05))
                };

                _tradePoints.Add(tradePoint);

                // 设置中心价格为最后一个测试交易的价格
                _centerPrice = tradePoint.Price;
            }

            // 更新价格范围
            UpdatePriceRange();

            System.Diagnostics.Debug.WriteLine($"AddTestData: 完成，添加了 {_tradePoints.Count} 个测试交易点，中心价格: {_centerPrice:F2}");
        }

        private void TradeChartView_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            RequestRedraw();
        }

        private void RedrawTimer_Tick(object? sender, EventArgs e)
        {
            _redrawTimer.Stop();
            if (_needsRedraw)
            {
                _needsRedraw = false;
                DrawChart();
            }
        }

        private void RequestRedraw()
        {
            _needsRedraw = true;
            _redrawTimer.Stop();
            _redrawTimer.Start();
        }

        private void RecentTrades_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // 减少调试输出的频率，避免控制台输出过多
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (TradeRecord trade in e.NewItems)
                {
                    AddTradePoint(trade);
                }
            }

            // 清理过期的交易点
            CleanupOldTrades();

            // 使用防抖机制请求重绘，避免频繁更新UI
            RequestRedraw();
        }

        private void AddTradePoint(TradeRecord trade)
        {
            var tradePoint = new TradePoint
            {
                Price = trade.Price,
                Quantity = trade.Quantity,
                Time = trade.Time,
                IsBuyerMaker = trade.IsBuyerMaker,
                Amount = trade.Price * trade.Quantity
            };

            _tradePoints.Add(tradePoint);

            // 更新中心价格为最新交易价格
            var oldCenterPrice = _centerPrice;
            _centerPrice = trade.Price;

            // 标记需要更新最大成交量缓存
            _maxQuantityNeedsUpdate = true;

            // 只有当价格变化较大且距离上次更新有足够间隔时才更新价格范围
            var now = DateTime.UtcNow;
            var shouldUpdateRange = Math.Abs(_centerPrice - oldCenterPrice) > _centerPrice * 0.001 && // 0.1%的变化阈值
                                   (now - _lastPriceRangeUpdate) >= _priceRangeUpdateInterval;

            if (shouldUpdateRange)
            {
                UpdatePriceRange();
                _lastPriceRangeUpdate = now;
            }
        }

        private void CleanupOldTrades()
        {
            // 使用UTC时间，因为交易记录的时间戳是UTC时间
            // 使用最大时间窗口来保留数据，这样缩放时不会丢失数据
            var cutoffTime = DateTime.UtcNow - _maxTimeWindow;
            var beforeCount = _tradePoints.Count;

            _tradePoints.RemoveAll(tp => tp.Time < cutoffTime);
            var afterCount = _tradePoints.Count;

            // 只在有实际清理时才输出调试信息和更新缓存
            if (beforeCount != afterCount)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView.CleanupOldTrades: 清理完成，删除了{beforeCount - afterCount}个交易点，剩余{afterCount}个");
                _maxQuantityNeedsUpdate = true; // 删除了交易点，需要重新计算最大成交量
            }

            // 更新价格范围以适应当前可见的交易点（考虑时间间隔）
            var now = DateTime.UtcNow;
            if ((now - _lastPriceRangeUpdate) >= _priceRangeUpdateInterval)
            {
                UpdatePriceRange();
                _lastPriceRangeUpdate = now;
            }
        }

        private void UpdatePriceRange()
        {
            if (_centerPrice <= 0 || _tradePoints.Count == 0)
            {
                _priceRange = _centerPrice > 0 ? _centerPrice * 0.02 : 100;
                return;
            }

            // 获取当前时间窗口内的可见交易点
            var now = DateTime.UtcNow;
            var visibleTrades = _tradePoints.Where(tp => (now - tp.Time) <= _timeWindow).ToList();

            if (!visibleTrades.Any())
            {
                // 如果没有可见交易点，使用基于中心价格的默认范围
                _priceRange = _centerPrice * 0.02;
                return;
            }

            // 计算可见交易点的价格范围
            var minVisiblePrice = visibleTrades.Min(tp => tp.Price);
            var maxVisiblePrice = visibleTrades.Max(tp => tp.Price);
            var visiblePriceRange = maxVisiblePrice - minVisiblePrice;

            // 计算最小价格范围（避免过度放大）
            var minPriceRange = _centerPrice * 0.005; // 0.5%的最小范围

            // 如果价格波动很小，使用最小范围
            if (visiblePriceRange < minPriceRange)
            {
                visiblePriceRange = minPriceRange;
            }

            // 添加5%的上下边距，并确保能利用90%的画布高度
            var paddedRange = visiblePriceRange * 1.1; // 添加10%的总边距（上下各5%）

            // 计算以中心价格为基准的对称范围
            // 确保所有可见交易点都能显示，同时保持中心价格在中心
            var halfRange = paddedRange / 2;
            var centerToMin = _centerPrice - minVisiblePrice;
            var centerToMax = maxVisiblePrice - _centerPrice;
            var maxCenterDistance = Math.Max(centerToMin, centerToMax);

            // 如果需要，扩大范围以包含所有可见交易点
            if (maxCenterDistance > halfRange)
            {
                halfRange = maxCenterDistance * 1.1; // 添加10%边距
            }

            var targetRange = halfRange * 2;

            // 平滑过渡：逐渐调整到目标范围，避免突然跳跃
            var maxChangeRate = 0.1; // 每次最多改变10%
            var rangeDifference = targetRange - _priceRange;
            var maxChange = _priceRange * maxChangeRate;

            if (Math.Abs(rangeDifference) > maxChange)
            {
                // 如果变化太大，分步调整
                _priceRange += Math.Sign(rangeDifference) * maxChange;
            }
            else
            {
                // 如果变化较小，直接调整到目标值
                _priceRange = targetRange;
            }

            // 确保最小范围
            _priceRange = Math.Max(_priceRange, minPriceRange);

            System.Diagnostics.Debug.WriteLine($"UpdatePriceRange: 中心={_centerPrice:F2}, 范围={_priceRange:F2}, 可见交易={visibleTrades.Count}, 目标范围={targetRange:F2}");
        }

        private void DrawChart()
        {
            System.Diagnostics.Debug.WriteLine($"DrawChart: 开始绘制图表，交易点数量: {_tradePoints.Count}, 画布尺寸: {TradeCanvas.ActualWidth}x{TradeCanvas.ActualHeight}");

            if (TradeCanvas.ActualWidth <= 0 || TradeCanvas.ActualHeight <= 0)
            {
                System.Diagnostics.Debug.WriteLine("DrawChart: 画布尺寸无效，跳过绘制");
                return;
            }

            // 清空画布
            TradeCanvas.Children.Clear();
            GridCanvas.Children.Clear();
            XAxisCanvas.Children.Clear();
            YAxisCanvas.Children.Clear();
            _ellipseToTradePoint.Clear();

            // 绘制网格线
            DrawGrid();

            // 绘制坐标轴
            DrawAxes();

            // 绘制交易点
            DrawTradePoints();
        }

        private void DrawGrid()
        {
            var width = TradeCanvas.ActualWidth;
            var height = TradeCanvas.ActualHeight;
            
            // 绘制水平网格线（价格）
            if (_priceRange > 0 && _centerPrice > 0)
            {
                var gridCount = 10;

                for (int i = 0; i <= gridCount; i++)
                {
                    var y = height - (i * height / gridCount);
                    var line = new Line
                    {
                        X1 = 0,
                        Y1 = y,
                        X2 = width,
                        Y2 = y,
                        Stroke = _gridBrush,
                        StrokeThickness = 0.5
                    };
                    GridCanvas.Children.Add(line);
                }
            }
            
            // 绘制垂直网格线（时间）
            var timeGridCount = 10;
            for (int i = 0; i <= timeGridCount; i++)
            {
                var x = i * width / timeGridCount;
                var line = new Line
                {
                    X1 = x,
                    Y1 = 0,
                    X2 = x,
                    Y2 = height,
                    Stroke = _gridBrush,
                    StrokeThickness = 0.5
                };
                GridCanvas.Children.Add(line);
            }
        }

        private void DrawAxes()
        {
            DrawYAxis();
            DrawXAxis();
        }

        private void DrawYAxis()
        {
            YAxisCanvas.Children.Clear();

            if (_priceRange <= 0 || _centerPrice <= 0) return;

            var height = YAxisCanvas.ActualHeight;
            var labelCount = 8;

            // 计算Y轴的价格范围，以中心价格为基准
            var halfRange = _priceRange / 2;
            var minPrice = _centerPrice - halfRange;
            var maxPrice = _centerPrice + halfRange;

            for (int i = 0; i <= labelCount; i++)
            {
                var price = minPrice + (_priceRange * i / labelCount);
                var y = height - (i * height / labelCount);
                
                var label = new TextBlock
                {
                    Text = price.ToString("F2"),
                    FontFamily = new FontFamily("BinancePlex, Microsoft YaHei"),
                    FontSize = 9,
                    Foreground = _axisBrush,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    VerticalAlignment = VerticalAlignment.Center
                };
                
                Canvas.SetTop(label, y - 6);
                Canvas.SetRight(label, 5);
                YAxisCanvas.Children.Add(label);
            }
        }

        private void DrawXAxis()
        {
            XAxisCanvas.Children.Clear();

            var width = XAxisCanvas.ActualWidth;
            if (width <= 0) return;

            var now = DateTime.UtcNow;

            // 计算合适的时间间隔，确保标签不会重叠
            // 对于30分钟窗口，我们每30秒显示一个标签
            var labelIntervalSeconds = 30; // 每30秒一个标签
            var totalSeconds = (int)_timeWindow.TotalSeconds;
            var labelCount = totalSeconds / labelIntervalSeconds;

            // 限制最大标签数量，避免过于密集
            if (labelCount > 20)
            {
                labelIntervalSeconds = totalSeconds / 20;
                labelCount = 20;
            }

            for (int i = 0; i <= labelCount; i++)
            {
                var timeOffset = i * labelIntervalSeconds;
                var time = now.AddSeconds(-totalSeconds + timeOffset);
                var x = (double)timeOffset / totalSeconds * width;

                var label = new TextBlock
                {
                    Text = time.ToString("HH:mm:ss"),
                    FontFamily = new FontFamily("BinancePlex, Microsoft YaHei"),
                    FontSize = 8,
                    Foreground = _axisBrush,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                Canvas.SetLeft(label, x - 20);
                Canvas.SetTop(label, 5);
                XAxisCanvas.Children.Add(label);
            }
        }

        private void DrawTradePoints()
        {
            if (_tradePoints.Count == 0 || _priceRange <= 0 || _centerPrice <= 0)
            {
                System.Diagnostics.Debug.WriteLine("DrawTradePoints: 跳过绘制 - 没有交易点或价格参数无效");
                return;
            }

            var width = TradeCanvas.ActualWidth;
            var height = TradeCanvas.ActualHeight;

            if (width <= 0 || height <= 0)
            {
                System.Diagnostics.Debug.WriteLine($"DrawTradePoints: 跳过绘制 - 画布尺寸无效: {width}x{height}");
                return;
            }

            var now = DateTime.UtcNow;
            var halfRange = _priceRange / 2;

            // 使用缓存的最大成交量，只在需要时重新计算
            if (_maxQuantityNeedsUpdate && _tradePoints.Count > 0)
            {
                _cachedMaxQuantity = _tradePoints.Max(tp => tp.Quantity);
                _maxQuantityNeedsUpdate = false;
            }

            var maxQuantity = _cachedMaxQuantity;
            var minSize = 1.0; // 最小1个像素点
            var maxSize = 20.0; // 最大20个像素点

            foreach (var tradePoint in _tradePoints)
            {
                // 计算位置
                var timeOffset = (now - tradePoint.Time).TotalSeconds;
                var x = width - (timeOffset / _timeWindow.TotalSeconds * width);

                // Y轴位置：以中心价格为基准计算
                // 中心价格显示在画布中心（height/2）
                var priceOffset = tradePoint.Price - _centerPrice;
                var y = (height / 2) - (priceOffset / halfRange * (height / 2));

                // 跳过超出范围的点
                if (x < 0 || x > width || y < 0 || y > height) continue;

                // 计算圆圈大小（基于成交量）
                var size = maxQuantity > 0 ? Math.Max(minSize, minSize + (tradePoint.Quantity / maxQuantity * (maxSize - minSize))) : minSize;



                // 创建圆圈
                var ellipse = new Ellipse
                {
                    Width = size,
                    Height = size,
                    Fill = tradePoint.IsBuyerMaker ? _sellBrush : _buyBrush, // 注意：IsBuyerMaker为true表示卖单
                    Stroke = tradePoint.IsBuyerMaker ? _sellBrush : _buyBrush,
                    StrokeThickness = 1
                };

                Canvas.SetLeft(ellipse, x - size / 2);
                Canvas.SetTop(ellipse, y - size / 2);

                TradeCanvas.Children.Add(ellipse);
                _ellipseToTradePoint[ellipse] = tradePoint;
            }
        }

        private void InteractionCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            var position = e.GetPosition(InteractionCanvas);
            var hitTest = VisualTreeHelper.HitTest(TradeCanvas, position);
            
            if (hitTest?.VisualHit is Ellipse ellipse && _ellipseToTradePoint.ContainsKey(ellipse))
            {
                var tradePoint = _ellipseToTradePoint[ellipse];
                ShowTooltip(tradePoint, position);
            }
            else
            {
                HideTooltip();
            }
        }

        private void InteractionCanvas_MouseLeave(object sender, MouseEventArgs e)
        {
            HideTooltip();
        }

        private void ShowTooltip(TradePoint tradePoint, Point position)
        {
            TooltipPrice.Text = $"价格: {tradePoint.Price:F5}";
            TooltipQuantity.Text = $"数量: {tradePoint.Quantity:F5}";
            TooltipTime.Text = $"时间: {tradePoint.Time:HH:mm:ss.fff}";
            
            Canvas.SetLeft(TooltipBorder, position.X + 10);
            Canvas.SetTop(TooltipBorder, position.Y - 10);
            TooltipBorder.Visibility = Visibility.Visible;
        }

        private void HideTooltip()
        {
            TooltipBorder.Visibility = Visibility.Collapsed;
        }

        private void TradeChartView_MouseEnter(object sender, MouseEventArgs e)
        {
            // 当鼠标进入时获取焦点，这样才能接收滚轮事件
            Focus();
        }

        private void ChartArea_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            // 调用原来的滚轮处理逻辑
            TradeChartView_MouseWheel(sender, e);
        }

        private void TradeChartView_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            // 获取鼠标滚轮方向
            var delta = e.Delta;

            // 计算缩放因子
            var zoomFactor = delta > 0 ? 0.8 : 1.25; // 向上滚动缩小时间窗口，向下滚动放大时间窗口

            // 计算新的时间窗口
            var newTimeWindow = TimeSpan.FromTicks((long)(_timeWindow.Ticks * zoomFactor));

            // 限制在最小和最大范围内
            if (newTimeWindow < _minTimeWindow)
                newTimeWindow = _minTimeWindow;
            else if (newTimeWindow > _maxTimeWindow)
                newTimeWindow = _maxTimeWindow;

            // 如果时间窗口发生变化，更新并重绘
            if (newTimeWindow != _timeWindow)
            {
                _timeWindow = newTimeWindow;
                System.Diagnostics.Debug.WriteLine($"时间窗口缩放: {_timeWindow.TotalMinutes:F1}分钟");

                // 更新时间窗口显示
                UpdateTimeWindowDisplay();

                // 清理过期的交易点
                CleanupOldTrades();

                // 使用防抖机制重新绘制图表
                RequestRedraw();
            }

            // 标记事件已处理
            e.Handled = true;
        }

        private void UpdateTimeWindowDisplay()
        {
            if (TimeWindowDisplay != null)
            {
                var totalMinutes = _timeWindow.TotalMinutes;
                string displayText;

                if (totalMinutes < 60)
                {
                    displayText = $"{totalMinutes:F0}分钟";
                }
                else
                {
                    var hours = totalMinutes / 60;
                    displayText = $"{hours:F1}小时";
                }

                TimeWindowDisplay.Text = displayText;
            }
        }
    }

    // 交易点数据结构
    public class TradePoint
    {
        public double Price { get; set; }
        public double Quantity { get; set; }
        public DateTime Time { get; set; }
        public bool IsBuyerMaker { get; set; }
        public double Amount { get; set; }
    }
}
